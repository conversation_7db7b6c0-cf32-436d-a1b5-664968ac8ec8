import torch
import torch.nn as nn

from model.attentionLayer import attentionLayer
from model.convLayer import ConvLayer
from model.visualEncoder import visualFrontend, visualConv1D, visualTCN

class locoencoder(nn.Module):

    def __init__(self, cfg):
        super(locoencoder, self).__init__()
        self.cfg = cfg
        # Visual Temporal Encoder
        self.visualFrontend = visualFrontend(cfg)    # Visual Frontend
        self.visualTCN = visualTCN()    # Visual Temporal Network TCN
        self.visualConv1D = visualConv1D()    # Visual Temporal Network Conv1d



        # Audio-visual Cross Attention


        # Audio-visual Self Attention
        num_layers = self.cfg.MODEL.AV_layers
        layers = nn.ModuleList()

        for i in range(num_layers):
            layers.append(ConvLayer(cfg))
            layers.append(attentionLayer(d_model=256, nhead=8))
        self.convV = layers

    def forward_visual_frontend(self, x):
        B, T, W, H = x.shape
        x = x.view(B * T, 1, 1, W, H)
        x = (x / 255 - 0.4161) / 0.1688

        x = self.visualFrontend(x)
        x = x.view(B, T, 512) # (B, T, C)
        x = x.transpose(1, 2) # (B, C, T)
        x = self.visualTCN(x)
        x = self.visualConv1D(x)
        x = x.transpose(1, 2) # (B, T, C)
        return x




    def forward_visual_backend(self, x, b=1, s=1):
        

        for i, layer in enumerate(self.convV):
            if i % 2 == 0:
                x, b, s = layer(x, b, s)
            else:
                x = layer(src=x, tar=x)

        x = torch.reshape(x, (-1, 256))

        return x

    def forward(self, x):
        x = self.forward_visual_frontend(x)

        x = self.forward_visual_backend(x)
        return x
