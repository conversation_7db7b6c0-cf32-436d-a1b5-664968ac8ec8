name: loconet
channels:
  - conda-forge
  - defaults
dependencies:
  - _libgcc_mutex=0.1=conda_forge
  - _openmp_mutex=4.5=1_gnu
  - alsa-lib=1.2.3=h516909a_0
  - anyio=3.5.0=py37h89c1867_0
  - argon2-cffi=21.3.0=pyhd8ed1ab_0
  - argon2-cffi-bindings=21.2.0=py37h5e8e339_1
  - aria2=1.36.0=h319415d_2
  - attrs=21.4.0=pyhd8ed1ab_0
  - babel=2.9.1=pyh44b312d_0
  - backcall=0.2.0=pyh9f0ad1d_0
  - backports=1.0=py_2
  - backports.functools_lru_cache=1.6.4=pyhd8ed1ab_0
  - bleach=4.1.0=pyhd8ed1ab_0
  - bottleneck=1.3.4=py37h6c7ee08_0
  - brotli=1.0.9=h7f98852_6
  - brotli-bin=1.0.9=h7f98852_6
  - brotlipy=0.7.0=py37h5e8e339_1003
  - c-ares=1.18.1=h7f98852_0
  - ca-certificates=2022.5.18.1=ha878542_0
  - cffi=1.14.6=py37hc58025e_0
  - configparser=5.2.0=pyhd8ed1ab_0
  - cryptography=36.0.1=py37hf1a17b8_0
  - cycler=0.11.0=pyhd8ed1ab_0
  - cython=0.29.27=py37hcd2ae1e_0
  - dbus=1.13.6=h48d8840_2
  - debugpy=1.5.1=py37hcd2ae1e_0
  - defusedxml=0.7.1=pyhd8ed1ab_0
  - easydict=1.9=py_0
  - entrypoints=0.4=pyhd8ed1ab_0
  - expat=2.4.6=h27087fc_0
  - flit-core=3.7.0=pyhd8ed1ab_0
  - fontconfig=2.13.96=ha180cfb_0
  - fonttools=4.29.1=py37h5e8e339_0
  - freetype=2.10.4=h0708190_1
  - gettext=********=h0b5b191_1005
  - giflib=5.2.1=h36c2ea0_2
  - glib=2.68.4=h9c3ff4c_0
  - glib-tools=2.68.4=h9c3ff4c_0
  - gst-plugins-base=1.18.5=hf529b03_0
  - gstreamer=1.18.5=h76c114f_0
  - icu=68.2=h9c3ff4c_0
  - idna=3.3=pyhd8ed1ab_0
  - importlib_resources=5.4.0=pyhd8ed1ab_0
  - ipykernel=6.9.1=py37h6531663_0
  - ipython=7.31.1=py37h89c1867_0
  - ipython_genutils=0.2.0=py_1
  - jbig=2.1=h7f98852_2003
  - jedi=0.18.1=py37h89c1867_0
  - jinja2=3.0.3=pyhd8ed1ab_0
  - jpeg=9e=h7f98852_0
  - json5=0.9.5=pyh9f0ad1d_0
  - jsonschema=4.4.0=pyhd8ed1ab_0
  - jupyter_client=7.1.2=pyhd8ed1ab_0
  - jupyter_core=4.9.2=py37h89c1867_0
  - jupyter_server=1.13.5=pyhd8ed1ab_1
  - jupyterlab=3.2.9=pyhd8ed1ab_0
  - jupyterlab_pygments=0.1.2=pyh9f0ad1d_0
  - jupyterlab_server=2.10.3=pyhd8ed1ab_0
  - kiwisolver=1.3.2=py37h2527ec5_1
  - krb5=1.19.2=hcc1bbae_3
  - lcms2=2.12=hddcbb42_0
  - ld_impl_linux-64=2.36.1=hea4e1c9_2
  - lerc=3.0=h9c3ff4c_0
  - libblas=3.9.0=13_linux64_openblas
  - libbrotlicommon=1.0.9=h7f98852_6
  - libbrotlidec=1.0.9=h7f98852_6
  - libbrotlienc=1.0.9=h7f98852_6
  - libcblas=3.9.0=13_linux64_openblas
  - libclang=11.1.0=default_ha53f305_1
  - libdeflate=1.10=h7f98852_0
  - libedit=3.1.20191231=he28a2e2_2
  - libevent=2.1.10=h9b69904_4
  - libffi=3.3=h58526e2_2
  - libgcc-ng=11.2.0=h1d223b6_12
  - libgfortran-ng=11.2.0=h69a702a_12
  - libgfortran5=11.2.0=h5c6108e_12
  - libglib=2.68.4=h3e27bee_0
  - libgomp=11.2.0=h1d223b6_12
  - libiconv=1.16=h516909a_0
  - liblapack=3.9.0=13_linux64_openblas
  - libllvm11=11.1.0=hf817b99_3
  - libogg=1.3.4=h7f98852_1
  - libopenblas=0.3.18=pthreads_h8fe5266_0
  - libopus=1.3.1=h7f98852_1
  - libpng=1.6.37=h21135ba_2
  - libpq=13.5=hd57d9b9_1
  - libsodium=1.0.18=h36c2ea0_1
  - libssh2=1.10.0=ha56f1ee_2
  - libstdcxx-ng=11.2.0=he4da1e4_12
  - libtiff=4.3.0=h542a066_3
  - libuuid=2.32.1=h7f98852_1000
  - libvorbis=1.3.7=h9c3ff4c_0
  - libwebp=1.2.2=h3452ae3_0
  - libwebp-base=1.2.2=h7f98852_1
  - libxcb=1.13=h7f98852_1004
  - libxkbcommon=1.0.3=he3ba5ed_0
  - libxml2=2.9.12=h72842e0_0
  - libzlib=1.2.11=h36c2ea0_1013
  - llvmlite=0.38.0=py37h0761922_1
  - lz4-c=1.9.3=h9c3ff4c_1
  - markupsafe=2.1.0=py37h540881e_0
  - matplotlib=3.5.1=py37h89c1867_0
  - matplotlib-base=3.5.1=py37h1058ff1_0
  - matplotlib-inline=0.1.3=pyhd8ed1ab_0
  - mistune=0.8.4=py37h5e8e339_1005
  - munkres=1.1.4=pyh9f0ad1d_0
  - mysql-common=8.0.28=ha770c72_0
  - mysql-libs=8.0.28=hfa10184_0
  - nbclassic=0.3.5=pyhd8ed1ab_0
  - nbclient=0.5.11=pyhd8ed1ab_0
  - nbconvert=6.4.2=py37h89c1867_0
  - nbformat=5.1.3=pyhd8ed1ab_0
  - ncurses=6.2=h58526e2_4
  - nest-asyncio=1.5.4=pyhd8ed1ab_0
  - nomkl=1.0=h5ca1d4c_0
  - notebook=6.4.8=pyha770c72_0
  - nspr=4.32=h9c3ff4c_1
  - nss=3.74=hb5efdd6_0
  - numba=0.55.1=py37h2d894fd_0
  - numexpr=2.8.0=py37hfe5f03c_101
  - numpy=1.21.5=py37hf2998dd_0
  - openjpeg=2.4.0=hb52868f_1
  - openssl=1.1.1o=h166bdaf_0
  - packaging=21.3=pyhd8ed1ab_0
  - pandas=1.3.5=py37h8c16a72_0
  - pandoc=********=ha770c72_0
  - pandocfilters=1.5.0=pyhd8ed1ab_0
  - parso=0.8.3=pyhd8ed1ab_0
  - patsy=0.5.2=pyhd8ed1ab_0
  - pcre=8.45=h9c3ff4c_0
  - pexpect=4.8.0=pyh9f0ad1d_2
  - pickleshare=0.7.5=py_1003
  - pip=22.0.3=pyhd8ed1ab_0
  - prometheus_client=0.13.1=pyhd8ed1ab_0
  - prompt-toolkit=3.0.27=pyha770c72_0
  - pthread-stubs=0.4=h36c2ea0_1001
  - ptyprocess=0.7.0=pyhd3deb0d_0
  - pycparser=2.21=pyhd8ed1ab_0
  - pygments=2.11.2=pyhd8ed1ab_0
  - pyopenssl=22.0.0=pyhd8ed1ab_0
  - pyparsing=3.0.7=pyhd8ed1ab_0
  - pyqt=5.12.3=py37h89c1867_8
  - pyqt-impl=5.12.3=py37hac37412_8
  - pyqt5-sip=4.19.18=py37hcd2ae1e_8
  - pyqtchart=5.12=py37he336c9b_8
  - pyqtwebengine=5.12.1=py37he336c9b_8
  - pyrsistent=0.18.1=py37h5e8e339_0
  - pysocks=1.7.1=py37h89c1867_4
  - python=3.7.9=hffdb5ce_100_cpython
  - python-dateutil=2.8.2=pyhd8ed1ab_0
  - python_abi=3.7=2_cp37m
  - pytz=2021.3=pyhd8ed1ab_0
  - pyzmq=22.3.0=py37h336d617_1
  - qt=5.12.9=hda022c4_4
  - readline=8.1=h46c0cb4_0
  - resampy=0.2.2=py_0
  - scipy=1.7.3=py37hf2a6cf1_0
  - seaborn=0.11.2=hd8ed1ab_0
  - seaborn-base=0.11.2=pyhd8ed1ab_0
  - send2trash=1.8.0=pyhd8ed1ab_0
  - six=1.16.0=pyh6c4a22f_0
  - sniffio=1.2.0=py37h89c1867_2
  - sqlite=3.37.0=h9cd32fc_0
  - statsmodels=0.13.2=py37hb1e94ed_0
  - terminado=0.13.1=py37h89c1867_0
  - testpath=0.5.0=pyhd8ed1ab_0
  - tk=8.6.12=h27826a3_0
  - tornado=6.1=py37h5e8e339_2
  - traitlets=5.1.1=pyhd8ed1ab_0
  - typing_extensions=4.1.1=pyha770c72_0
  - unicodedata2=14.0.0=py37h5e8e339_0
  - wcwidth=0.2.5=pyh9f0ad1d_2
  - webencodings=0.5.1=py_1
  - websocket-client=1.2.3=pyhd8ed1ab_0
  - wheel=0.37.1=pyhd8ed1ab_0
  - xorg-libxau=1.0.9=h7f98852_0
  - xorg-libxdmcp=1.1.3=h7f98852_0
  - xz=5.2.5=h516909a_1
  - zeromq=4.3.4=h9c3ff4c_1
  - zlib=1.2.11=h36c2ea0_1013
  - zstd=1.5.2=ha95c52a_0
  - pip:
    - absl-py==1.0.0
    - addict==2.4.0
    - aiohttp==3.8.1
    - aiosignal==1.2.0
    - analytics-python==1.4.0
    - appdirs==1.4.4
    - asgiref==3.5.2
    - async-timeout==4.0.2
    - asynctest==0.13.0
    - audioread==2.1.9
    - backoff==1.10.0
    - bcrypt==3.2.2
    - beautifulsoup4==4.10.0
    - cachetools==4.2.4
    - certifi==2021.10.8
    - charset-normalizer==2.0.9
    - click==8.0.3
    - decorator==4.4.2
    - decord==0.6.0
    - einops==0.4.0
    - fastapi==0.78.0
    - ffmpeg==1.4
    - ffmpy==0.3.0
    - filelock==3.4.0
    - frozenlist==1.3.0
    - fsspec==2022.1.0
    - future==0.18.2
    - fvcore==0.1.5.post20221221
    - gdown==4.2.0
    - google-auth==2.3.3
    - google-auth-oauthlib==0.4.6
    - gradio==3.0.2
    - grpcio==1.43.0
    - h11==0.13.0
    - imageio==2.23.0
    - imageio-ffmpeg==0.4.7
    - importlib-metadata==4.10.0
    - iopath==0.1.10
    - ipywidgets==8.0.4
    - joblib==1.1.0
    - jupyterlab-widgets==3.0.5
    - librosa==0.9.1
    - linkify-it-py==1.0.3
    - lmdb==1.4.1
    - markdown==3.3.6
    - markdown-it-py==2.1.0
    - mdit-py-plugins==0.3.0
    - mdurl==0.1.1
    - mmaction2==0.24.1
    - mmcv==1.7.0
    - mmcv-full==1.4.6
    - monotonic==1.6
    - moviepy==1.0.3
    - multidict==5.2.0
    - oauthlib==3.1.1
    - opencv-contrib-python==********
    - opencv-python==********
    - orjson==3.6.8
    - paramiko==2.11.0
    - pillow==8.3.2
    - pooch==1.6.0
    - portalocker==2.7.0
    - proglog==0.1.10
    - protobuf==3.19.3
    - pyasn1==0.4.8
    - pyasn1-modules==0.2.8
    - pycryptodome==3.14.1
    - pydantic==1.9.0
    - pydeprecate==0.3.1
    - pydub==0.25.1
    - pynacl==1.5.0
    - python-box==6.0.2
    - python-multipart==0.0.5
    - python-speech-features==0.6
    - pytorch-lightning==1.5.8
    - pyyaml==6.0
    - requests==2.26.0
    - requests-oauthlib==1.3.0
    - rsa==4.8
    - scenedetect==*******
    - scikit-learn==1.0.1
    - setuptools==60.9.3
    - soundfile==0.10.3.post1
    - soupsieve==2.3.1
    - starlette==0.19.1
    - tabulate==0.9.0
    - tensorboard==2.7.0
    - tensorboard-data-server==0.6.1
    - tensorboard-plugin-wit==1.8.1
    - termcolor==2.2.0
    - threadpoolctl==3.0.0
    - timm==0.4.5
    - torch==1.10.1
    - torchaudio==0.10.1
    - torchlibrosa==0.0.9
    - torchmetrics==0.7.0
    - torchvision==0.11.2
    - tqdm==4.62.3
    - typing-extensions==4.0.1
    - uc-micro-py==1.0.1
    - urllib3==1.26.7
    - uvicorn==0.17.6
    - warmup-scheduler-pytorch==0.1.2
    - werkzeug==2.0.2
    - wget==3.2
    - widgetsnbextension==4.0.5
    - yacs==0.1.8
    - yapf==0.32.0
    - yarl==1.7.2
    - youtube-dl==2021.12.17
    - zipp==3.6.0
