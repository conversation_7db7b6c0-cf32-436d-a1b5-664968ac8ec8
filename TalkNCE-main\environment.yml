name: loconet
channels:
  - conda-forge
  - defaults
dependencies:
  - _libgcc_mutex=0.1
  - _openmp_mutex=4.5
  - alsa-lib=1.2.3
  - anyio=3.5.0
  - appdirs=1.4.4
  - argon2-cffi=21.3.0
  - argon2-cffi-bindings=21.2.0
  - aria2=1.36.0
  - attrs=21.4.0
  - babel=2.9.1
  - backcall=0.2.0
  - backports=1.0
  - backports.functools_lru_cache=1.6.4
  - bleach=4.1.0
  - bottleneck=1.3.4
  - brotli=1.0.9
  - brotli-bin=1.0.9
  - brotlipy=0.7.0
  - bzip2=1.0.8
  - c-ares=1.18.1
  - ca-certificates=2023.5.7
  - cffi=1.14.6
  - configparser=5.2.0
  - cryptography=36.0.1
  - cycler=0.11.0
  - cython=0.29.27
  - dbus=1.13.6
  - debugpy=1.5.1
  - defusedxml=0.7.1
  - easydict=1.9
  - entrypoints=0.4
  - expat=2.4.6
  - flit-core=3.7.0
  - fontconfig=2.13.96
  - fonttools=4.29.1
  - freetype=2.10.4
  - gettext=********
  - giflib=5.2.1
  - glib=2.68.4
  - glib-tools=2.68.4
  - gmp=6.2.1
  - gnutls=3.6.13
  - gst-plugins-base=1.18.5
  - gstreamer=1.18.5
  - icu=68.2
  - idna=3.3
  - importlib_resources=5.4.0
  - ipykernel=6.9.1
  - ipython=7.31.1
  - ipython_genutils=0.2.0
  - jbig=2.1
  - jedi=0.18.1
  - jinja2=3.0.3
  - jpeg=9e
  - json5=0.9.5
  - jsonschema=4.4.0
  - jupyter_client=7.1.2
  - jupyter_core=4.9.2
  - jupyter_server=1.13.5
  - jupyterlab=3.2.9
  - jupyterlab_pygments=0.1.2
  - jupyterlab_server=2.10.3
  - kiwisolver=1.3.2
  - krb5=1.19.2
  - lame=3.100
  - lcms2=2.12
  - ld_impl_linux-64=2.36.1
  - lerc=3.0
  - libblas=3.9.0
  - libbrotlicommon=1.0.9
  - libbrotlidec=1.0.9
  - libbrotlienc=1.0.9
  - libcblas=3.9.0
  - libclang=11.1.0
  - libdeflate=1.10
  - libedit=3.1.20191231
  - libevent=2.1.10
  - libffi=3.3
  - libflac=1.3.4
  - libgcc-ng=11.2.0
  - libgfortran-ng=11.2.0
  - libgfortran5=11.2.0
  - libglib=2.68.4
  - libgomp=11.2.0
  - libiconv=1.16
  - liblapack=3.9.0
  - libllvm11=11.1.0
  - libogg=1.3.4
  - libopenblas=0.3.18
  - libopus=1.3.1
  - libpng=1.6.37
  - libpq=13.5
  - libsndfile=1.0.31
  - libsodium=1.0.18
  - libssh2=1.10.0
  - libstdcxx-ng=11.2.0
  - libtiff=4.3.0
  - libuuid=2.32.1
  - libvorbis=1.3.7
  - libvpx=1.11.0
  - libwebp=1.2.2
  - libwebp-base=1.2.2
  - libxcb=1.13
  - libxkbcommon=1.0.3
  - libxml2=2.9.12
  - libzlib=1.2.11
  - llvmlite=0.38.0
  - lz4-c=1.9.3
  - markupsafe=2.1.0
  - matplotlib=3.5.1
  - matplotlib-base=3.5.1
  - matplotlib-inline=0.1.3
  - mistune=0.8.4
  - munkres=1.1.4
  - mysql-common=8.0.28
  - mysql-libs=8.0.28
  - nbclassic=0.3.5
  - nbclient=0.5.11
  - nbconvert=6.4.2
  - nbformat=5.1.3
  - ncurses=6.2
  - nest-asyncio=1.5.4
  - nettle=3.6
  - nomkl=1.0
  - notebook=6.4.8
  - nspr=4.32
  - nss=3.74
  - numba=0.55.1
  - numexpr=2.8.0
  - numpy=1.21.5
  - openh264=2.1.1
  - openjpeg=2.4.0
  - openssl=1.1.1o
  - packaging=21.3
  - pandas=1.3.5
  - pandoc=********
  - pandocfilters=1.5.0
  - parso=0.8.3
  - patsy=0.5.2
  - pcre=8.45
  - pexpect=4.8.0
  - pickleshare=0.7.5
  - pip=22.0.3
  - pooch=1.6.0
  - prometheus_client=0.13.1
  - prompt-toolkit=3.0.27
  - pthread-stubs=0.4
  - ptyprocess=0.7.0
  - pycparser=2.21
  - pygments=2.11.2
  - pyopenssl=22.0.0
  - pyparsing=3.0.7
  - pyqt=5.12.3
  - pyqt-impl=5.12.3
  - pyqt5-sip=4.19.18
  - pyqtchart=5.12
  - pyqtwebengine=5.12.1
  - pyrsistent=0.18.1
  - pysocks=1.7.1
  - pysoundfile=0.11.0
  - python=3.7.9
  - python-dateutil=2.8.2
  - python_abi=3.7
  - pytz=2021.3
  - pyzmq=22.3.0
  - qt=5.12.9
  - readline=8.1
  - resampy=0.2.2
  - scipy=1.7.3
  - seaborn=0.11.2
  - seaborn-base=0.11.2
  - send2trash=1.8.0
  - six=1.16.0
  - sniffio=1.2.0
  - sqlite=3.37.0
  - statsmodels=0.13.2
  - terminado=0.13.1
  - testpath=0.5.0
  - tk=8.6.12
  - tornado=6.1
  - traitlets=5.1.1
  - unicodedata2=14.0.0
  - wcwidth=0.2.5
  - webencodings=0.5.1
  - websocket-client=1.2.3
  - wheel=0.37.1
  - x264=1!161.3030
  - x265=3.5
  - xorg-libxau=1.0.9
  - xorg-libxdmcp=1.1.3
  - xz=5.2.5
  - zeromq=4.3.4
  - zlib=1.2.11
  - zstd=1.5.2
  - pip:
      - absl-py==1.0.0
      - addict==2.4.0
      - aiohttp==3.8.1
      - aiosignal==1.2.0
      - analytics-python==1.4.0
      - asgiref==3.5.2
      - async-timeout==4.0.2
      - asynctest==0.13.0
      - audioread==2.1.9
      - backoff==1.10.0
      - bcrypt==3.2.2
      - beautifulsoup4==4.10.0
      - cachetools==4.2.4
      - certifi==2021.10.8
      - charset-normalizer==2.0.9
      - click==8.0.3
      - decorator==4.4.2
      - decord==0.6.0
      - einops==0.4.0
      - fastapi==0.78.0
      - ffmpeg==1.4
      - ffmpy==0.3.0
      - filelock==3.4.0
      - frozenlist==1.3.0
      - fsspec==2022.1.0
      - future==0.18.2
      - fvcore==0.1.5.post20221221
      - gdown==4.2.0
      - google-auth==2.3.3
      - google-auth-oauthlib==0.4.6
      - gradio==3.0.2
      - grpcio==1.43.0
      - h11==0.13.0
      - imageio==2.23.0
      - imageio-ffmpeg==0.4.7
      - importlib-metadata==4.10.0
      - iopath==0.1.10
      - ipywidgets==8.0.4
      - joblib==1.1.0
      - jupyterlab-widgets==3.0.5
      - librosa==0.9.1
      - linkify-it-py==1.0.3
      - lmdb==1.4.1
      - markdown==3.3.6
      - markdown-it-py==2.1.0
      - mdit-py-plugins==0.3.0
      - mdurl==0.1.1
      - mmaction2==0.24.1
      - mmcv==1.7.0
      - mmcv-full==1.4.6
      - monotonic==1.6
      - moviepy==1.0.3
      - multidict==5.2.0
      - oauthlib==3.1.1
      - opencv-contrib-python==********
      - opencv-python==********
      - orjson==3.6.8
      - paramiko==2.11.0
      - pillow==9.5.0
      - portalocker==2.7.0
      - proglog==0.1.10
      - protobuf==3.19.3
      - pyasn1==0.4.8
      - pyasn1-modules==0.2.8
      - pycryptodome==3.14.1
      - pydantic==1.9.0
      - pydeprecate==0.3.1
      - pydub==0.25.1
      - pynacl==1.5.0
      - python-box==6.0.2
      - python-multipart==0.0.5
      - python-speech-features==0.6
      - pytorch-lightning==1.5.8
      - pytube==15.0.0
      - pyyaml==6.0
      - requests==2.26.0
      - requests-oauthlib==1.3.0
      - rsa==4.8
      - scenedetect==*******
      - scikit-learn==1.0.1
      - setuptools==60.9.3
      - soundfile==0.10.3.post1
      - soupsieve==2.3.1
      - starlette==0.19.1
      - tabulate==0.9.0
      - tensorboard==2.7.0
      - tensorboard-data-server==0.6.1
      - tensorboard-plugin-wit==1.8.1
      - termcolor==2.2.0
      - threadpoolctl==3.0.0
      - timm==0.4.5
      - torch==1.11.0
      - torchaudio==0.11.0
      - torchlibrosa==0.0.9
      - torchmetrics==0.7.0
      - torchvision==0.12.0
      - tqdm==4.62.3
      - typing-extensions==4.0.1
      - uc-micro-py==1.0.1
      - urllib3==1.26.7
      - uvicorn==0.17.6
      - warmup-scheduler-pytorch==0.1.2
      - werkzeug==2.0.2
      - wget==3.2
      - widgetsnbextension==4.0.5
      - yacs==0.1.8
      - yapf==0.32.0
      - yarl==1.7.2
      - youtube-dl==2021.12.17
      - zipp==3.6.0
prefix: /home/<USER>/anaconda3/envs/loconet
