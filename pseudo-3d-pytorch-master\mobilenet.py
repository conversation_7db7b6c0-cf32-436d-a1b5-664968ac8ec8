import torch
import torch.nn as nn
import torch.nn.functional as F
import math
from functools import partial

# P3D-style convolution functions
def conv_S_3d(in_planes, out_planes, stride=1, padding=1):
    """Spatial convolution: 1x3x3"""
    return nn.Conv3d(in_planes, out_planes, kernel_size=(1,3,3), stride=1,
                     padding=padding, bias=False)

def conv_T_3d(in_planes, out_planes, stride=1, padding=1):
    """Temporal convolution: 3x1x1"""
    return nn.Conv3d(in_planes, out_planes, kernel_size=(3,1,1), stride=1,
                     padding=padding, bias=False)

def downsample_basic_block_3d(x, planes, stride):
    """3D downsample function"""
    out = F.avg_pool3d(x, kernel_size=1, stride=stride)
    zero_pads = torch.zeros(out.size(0), planes - out.size(1),
                           out.size(2), out.size(3), out.size(4))
    if isinstance(out.data, torch.cuda.FloatTensor):
        zero_pads = zero_pads.cuda()
    out = torch.cat([out.data, zero_pads], dim=1)
    return out

class ConvBNReLU(nn.Sequential):
    def __init__(self, in_planes, out_planes, kernel_size=3, stride=1, groups=1):
        padding = (kernel_size - 1) // 2
        super(ConvBNReLU, self).__init__(
            nn.Conv2d(in_planes, out_planes, kernel_size, stride, padding, groups=groups, bias=False),
            nn.BatchNorm2d(out_planes),
            nn.ReLU6(inplace=True)
        )

class ConvBNReLU3D(nn.Sequential):
    """3D version of ConvBNReLU"""
    def __init__(self, in_planes, out_planes, kernel_size=3, stride=1, groups=1):
        if isinstance(kernel_size, int):
            padding = (kernel_size - 1) // 2
        else:
            padding = tuple((k - 1) // 2 for k in kernel_size)
        super(ConvBNReLU3D, self).__init__(
            nn.Conv3d(in_planes, out_planes, kernel_size, stride, padding, groups=groups, bias=False),
            nn.BatchNorm3d(out_planes),
            nn.ReLU6(inplace=True)
        )

class SqueezeExcitation(nn.Module):
    def __init__(self, in_channels, reduced_dim):
        super(SqueezeExcitation, self).__init__()
        self.se = nn.Sequential(
            nn.AdaptiveAvgPool2d(1),
            nn.Conv2d(in_channels, reduced_dim, 1),
            nn.ReLU6(inplace=True),
            nn.Conv2d(reduced_dim, in_channels, 1),
            nn.Sigmoid()
        )

    def forward(self, x):
        return x * self.se(x)

class SqueezeExcitation3D(nn.Module):
    """3D version of Squeeze-and-Excitation"""
    def __init__(self, in_channels, reduced_dim):
        super(SqueezeExcitation3D, self).__init__()
        self.se = nn.Sequential(
            nn.AdaptiveAvgPool3d(1),
            nn.Conv3d(in_channels, reduced_dim, 1),
            nn.ReLU6(inplace=True),
            nn.Conv3d(reduced_dim, in_channels, 1),
            nn.Sigmoid()
        )

    def forward(self, x):
        return x * self.se(x)

class InvertedResidual(nn.Module):
    def __init__(self, inp, oup, stride, expand_ratio):
        super(InvertedResidual, self).__init__()
        self.stride = stride
        assert stride in [1, 2]

        hidden_dim = int(round(inp * expand_ratio))
        self.use_res_connect = self.stride == 1 and inp == oup

        layers = []
        if expand_ratio != 1:
            layers.append(ConvBNReLU(inp, hidden_dim, kernel_size=1))

        layers.extend([
            ConvBNReLU(hidden_dim, hidden_dim, stride=stride, groups=hidden_dim),
            SqueezeExcitation(hidden_dim, max(1, hidden_dim // 4)),
            nn.Conv2d(hidden_dim, oup, 1, 1, 0, bias=False),
            nn.BatchNorm2d(oup),
        ])
        self.conv = nn.Sequential(*layers)

    def forward(self, x):
        if self.use_res_connect:
            return x + self.conv(x)
        else:
            return self.conv(x)

class InvertedResidualP3D(nn.Module):
    """P3D-style InvertedResidual with pseudo-3D convolutions"""
    def __init__(self, inp, oup, stride, expand_ratio, n_s=0, depth_3d=10, ST_struc=('A','B','C')):
        super(InvertedResidualP3D, self).__init__()
        self.stride = stride
        self.n_s = n_s
        self.depth_3d = depth_3d
        self.ST_struc = ST_struc
        self.len_ST = len(self.ST_struc)
        self.ST = list(self.ST_struc)[self.n_s % self.len_ST]

        assert stride in [1, 2]
        hidden_dim = int(round(inp * expand_ratio))
        self.use_res_connect = self.stride == 1 and inp == oup

        # Determine if this layer should use 3D or 2D convolutions
        self.is_3d = n_s < depth_3d

        if self.is_3d:
            # 3D version with P3D decomposition
            layers = []
            if expand_ratio != 1:
                layers.append(ConvBNReLU3D(inp, hidden_dim, kernel_size=1))

            # P3D-style depthwise convolution
            self.depthwise_conv_s = conv_S_3d(hidden_dim, hidden_dim, padding=(0,1,1))
            self.bn_s = nn.BatchNorm3d(hidden_dim)
            self.depthwise_conv_t = conv_T_3d(hidden_dim, hidden_dim, padding=(1,0,0))
            self.bn_t = nn.BatchNorm3d(hidden_dim)
            self.relu = nn.ReLU6(inplace=True)

            self.se = SqueezeExcitation3D(hidden_dim, max(1, hidden_dim // 4))
            self.pointwise_conv = nn.Conv3d(hidden_dim, oup, 1, 1, 0, bias=False)
            self.bn_pointwise = nn.BatchNorm3d(oup)

            if expand_ratio != 1:
                self.expand_conv = nn.Sequential(*layers)
            else:
                self.expand_conv = None
        else:
            # 2D version (fallback to original)
            layers = []
            if expand_ratio != 1:
                layers.append(ConvBNReLU(inp, hidden_dim, kernel_size=1))

            layers.extend([
                ConvBNReLU(hidden_dim, hidden_dim, stride=stride, groups=hidden_dim),
                SqueezeExcitation(hidden_dim, max(1, hidden_dim // 4)),
                nn.Conv2d(hidden_dim, oup, 1, 1, 0, bias=False),
                nn.BatchNorm2d(oup),
            ])
            self.conv = nn.Sequential(*layers)

    def ST_A(self, x):
        """ST-A: Sequential S->T"""
        x = self.depthwise_conv_s(x)
        x = self.bn_s(x)
        x = self.relu(x)

        x = self.depthwise_conv_t(x)
        x = self.bn_t(x)
        x = self.relu(x)
        return x

    def ST_B(self, x):
        """ST-B: Parallel S+T"""
        tmp_x = self.depthwise_conv_s(x)
        tmp_x = self.bn_s(tmp_x)
        tmp_x = self.relu(tmp_x)

        x = self.depthwise_conv_t(x)
        x = self.bn_t(x)
        x = self.relu(x)

        return x + tmp_x

    def ST_C(self, x):
        """ST-C: Sequential S->T with residual"""
        x = self.depthwise_conv_s(x)
        x = self.bn_s(x)
        x = self.relu(x)

        tmp_x = self.depthwise_conv_t(x)
        tmp_x = self.bn_t(tmp_x)
        tmp_x = self.relu(tmp_x)

        return x + tmp_x

    def forward(self, x):
        if self.is_3d:
            # 3D processing with P3D decomposition
            residual = x

            # Expansion
            if self.expand_conv is not None:
                out = self.expand_conv(x)
            else:
                out = x

            # P3D depthwise convolution
            if self.ST == 'A':
                out = self.ST_A(out)
            elif self.ST == 'B':
                out = self.ST_B(out)
            elif self.ST == 'C':
                out = self.ST_C(out)

            # SE module
            out = self.se(out)

            # Pointwise convolution
            out = self.pointwise_conv(out)
            out = self.bn_pointwise(out)

            if self.use_res_connect:
                return residual + out
            else:
                return out
        else:
            # 2D processing - need to handle 3D->2D conversion
            # Convert 5D tensor to 4D if necessary
            if len(x.shape) == 5:  # (B, C, T, H, W)
                B, C, T, H, W = x.shape
                x = x.view(B * T, C, H, W)  # (B*T, C, H, W)

            if self.use_res_connect:
                return x + self.conv(x)
            else:
                return self.conv(x)

class MobileNetV3_Large(nn.Module):
    def __init__(self, num_classes=256):
        super(MobileNetV3_Large, self).__init__()
        
        # Modified first layer with 8 input channels
        self.features = nn.Sequential(
            ConvBNReLU(8, 16, 3, stride=2),
            InvertedResidual(16, 16, 1, expand_ratio=1),
            InvertedResidual(16, 24, 2, expand_ratio=4),
            InvertedResidual(24, 24, 1, expand_ratio=3),
            InvertedResidual(24, 40, 2, expand_ratio=3),
            InvertedResidual(40, 40, 1, expand_ratio=3),
            InvertedResidual(40, 40, 1, expand_ratio=3),
            InvertedResidual(40, 80, 2, expand_ratio=6),
            InvertedResidual(80, 80, 1, expand_ratio=2.5),
            InvertedResidual(80, 80, 1, expand_ratio=2.3),
            InvertedResidual(80, 80, 1, expand_ratio=2.3),
            InvertedResidual(80, 112, 1, expand_ratio=6),
            InvertedResidual(112, 112, 1, expand_ratio=6),
            InvertedResidual(112, 160, 2, expand_ratio=6),
            InvertedResidual(160, 160, 1, expand_ratio=6),
            InvertedResidual(160, 160, 1, expand_ratio=6),
            ConvBNReLU(160, 960, kernel_size=1),
            nn.AdaptiveAvgPool2d(1)
        )
        
        self.classifier = nn.Sequential(
            nn.Linear(960, 1280),
            nn.Hardswish(inplace=True),
            nn.Dropout(p=0.2, inplace=True),
            nn.Linear(1280, num_classes),
        )

        self.__init_weight()

    def forward(self, x):
        x = (x / 255 - 0.4161) / 0.1688
        
        x = self.features(x)
        x = x.flatten(1)
        x = self.classifier(x)
        return x
    
    def __init_weight(self):
        for m in self.modules():
            if isinstance(m, nn.Conv3d) or isinstance(m, nn.Conv2d) or isinstance(m, nn.Linear):
                torch.nn.init.kaiming_normal_(m.weight)              
            elif isinstance(m, nn.BatchNorm3d) or isinstance(m, nn.LayerNorm):
                m.weight.data.fill_(1)
                m.bias.data.zero_()

def mobilenet_v3_large(**kwargs):
    model = MobileNetV3_Large(**kwargs)
    return model

def export_to_onnx(model, output_path="mobilenetv3_large.onnx"):
    """Export MobileNetV3_Large model to ONNX format"""
    # Create dummy input with shape (1,8,112,112)
    dummy_input = torch.randn(1, 8, 112, 112)
    
    # Export the model
    torch.onnx.export(
        model,
        dummy_input,
        output_path,
        input_names=["input"],
        output_names=["output"],
        # dynamic_axes={
        #     "input": {0: "batch_size"},
        #     "output": {0: "batch_size"}
        # }
    )
    print(f"Model exported to {output_path}")

class MobileNetV3_Small(nn.Module):
    def __init__(self, num_classes=256):
        super(MobileNetV3_Small, self).__init__()
        # 参考官方结构，输入通道为8
        self.features = nn.Sequential(
            ConvBNReLU(8, 16, 3, stride=2),
            InvertedResidual(16, 16, 2, expand_ratio=1),
            InvertedResidual(16, 24, 2, expand_ratio=4.5),
            InvertedResidual(24, 24, 1, expand_ratio=3.67),
            InvertedResidual(24, 40, 2, expand_ratio=4),
            InvertedResidual(40, 40, 1, expand_ratio=6),
            InvertedResidual(40, 40, 1, expand_ratio=6),
            InvertedResidual(40, 48, 1, expand_ratio=3),
            InvertedResidual(48, 48, 1, expand_ratio=3),
            InvertedResidual(48, 96, 2, expand_ratio=6),
            InvertedResidual(96, 96, 1, expand_ratio=6),
            InvertedResidual(96, 96, 1, expand_ratio=6),
            ConvBNReLU(96, 576, kernel_size=1),
            nn.AdaptiveAvgPool2d(1)
        )
        self.classifier = nn.Sequential(
            nn.Linear(576, 1024),
            nn.Hardswish(inplace=True),
            nn.Dropout(p=0.2, inplace=True),
            nn.Linear(1024, num_classes),
        )
        self.__init_weight()

    def forward(self, x):
        x = (x / 255 - 0.4161) / 0.1688
        x = self.features(x)
        x = x.flatten(1)
        x = self.classifier(x)
        return x

    def __init_weight(self):
        for m in self.modules():
            if isinstance(m, nn.Conv3d) or isinstance(m, nn.Conv2d) or isinstance(m, nn.Linear):
                torch.nn.init.kaiming_normal_(m.weight)
            elif isinstance(m, nn.BatchNorm3d) or isinstance(m, nn.LayerNorm):
                m.weight.data.fill_(1)
                m.bias.data.zero_()

class MobileNetV3_Small_P3D(nn.Module):
    """P3D version of MobileNetV3_Small for video processing"""
    def __init__(self, num_classes=256, modality='RGB', depth_3d=8, ST_struc=('A','B','C')):
        super(MobileNetV3_Small_P3D, self).__init__()

        self.input_channel = 3 if modality == 'RGB' else 2  # 2 for optical flow
        self.depth_3d = depth_3d  # Number of layers to use 3D convolutions
        self.ST_struc = ST_struc

        # First 3D convolution layer (similar to P3D)
        self.conv1_custom = nn.Conv3d(self.input_channel, 16, kernel_size=(1,3,3),
                                     stride=(1,2,2), padding=(0,1,1), bias=False)
        self.bn1 = nn.BatchNorm3d(16)
        self.relu = nn.ReLU6(inplace=True)

        # Temporal pooling for early layers
        self.maxpool_3d = nn.MaxPool3d(kernel_size=(2,1,1), stride=(2,1,1), padding=0)

        # Build inverted residual blocks
        self.features = self._make_layers()

        # Final pooling (2D for last layers)
        self.avgpool = nn.AdaptiveAvgPool2d(1)

        self.classifier = nn.Sequential(
            nn.Linear(576, 1024),
            nn.Hardswish(inplace=True),
            nn.Dropout(p=0.2, inplace=True),
            nn.Linear(1024, num_classes),
        )

        self.__init_weight()

        # Input properties (similar to P3D)
        self.input_size = (self.input_channel, 16, 112, 112)
        self.input_mean = [0.485, 0.456, 0.406] if modality=='RGB' else [0.5]
        self.input_std = [0.229, 0.224, 0.225] if modality=='RGB' else [0.229]

    def _make_layers(self):
        """Build the inverted residual layers with P3D support"""
        # MobileNetV3-Small configuration: (inp, oup, stride, expand_ratio)
        configs = [
            (16, 16, 2, 1),      # layer 0
            (16, 24, 2, 4.5),    # layer 1
            (24, 24, 1, 3.67),   # layer 2
            (24, 40, 2, 4),      # layer 3
            (40, 40, 1, 6),      # layer 4
            (40, 40, 1, 6),      # layer 5
            (40, 48, 1, 3),      # layer 6
            (48, 48, 1, 3),      # layer 7
            (48, 96, 2, 6),      # layer 8
            (96, 96, 1, 6),      # layer 9
            (96, 96, 1, 6),      # layer 10
        ]

        layers = []
        for i, (inp, oup, stride, expand_ratio) in enumerate(configs):
            layers.append(InvertedResidualP3D(
                inp, oup, stride, expand_ratio,
                n_s=i, depth_3d=self.depth_3d, ST_struc=self.ST_struc
            ))

        # Final 1x1 convolution
        if len(configs) - 1 < self.depth_3d:
            # Still in 3D mode
            layers.append(ConvBNReLU3D(96, 576, kernel_size=1))
        else:
            # Switch to 2D mode
            layers.append(ConvBNReLU(96, 576, kernel_size=1))

        return nn.Sequential(*layers)

    def forward(self, x):
        # Initial 3D convolution
        x = self.conv1_custom(x)
        x = self.bn1(x)
        x = self.relu(x)

        # Apply temporal pooling periodically
        x = self.maxpool_3d(x)

        # Process through inverted residual blocks
        for i, layer in enumerate(self.features):
            # Check if we need to convert from 3D to 2D at the transition point
            if i == self.depth_3d and len(x.shape) == 5:
                # Convert from 3D to 2D at the transition
                B, C, T, H, W = x.shape
                x = x.view(B * T, C, H, W)  # (B*T, C, H, W)

            x = layer(x)

            # Apply temporal pooling for 3D layers
            if i < self.depth_3d and i % 3 == 2:  # Every 3rd 3D layer
                if len(x.shape) == 5 and x.size(2) > 1:  # Only if temporal dimension > 1
                    x = self.maxpool_3d(x)

        # Final conversion to 2D if still in 3D (shouldn't happen but safety check)
        if len(x.shape) == 5:  # 3D tensor (B, C, T, H, W)
            B, C, T, H, W = x.shape
            x = x.view(B * T, C, H, W)  # (B*T, C, H, W)

        # Final pooling and classification
        x = self.avgpool(x)
        x = x.flatten(1)
        x = self.classifier(x)

        return x

    def __init_weight(self):
        for m in self.modules():
            if isinstance(m, (nn.Conv3d, nn.Conv2d)):
                n = m.kernel_size[0] * m.kernel_size[1] * m.out_channels
                if isinstance(m, nn.Conv3d):
                    n *= m.kernel_size[2]
                m.weight.data.normal_(0, math.sqrt(2. / n))
            elif isinstance(m, (nn.BatchNorm3d, nn.BatchNorm2d)):
                m.weight.data.fill_(1)
                m.bias.data.zero_()
            elif isinstance(m, nn.Linear):
                torch.nn.init.kaiming_normal_(m.weight)

def mobilenet_v3_small(**kwargs):
    model = MobileNetV3_Small(**kwargs)
    return model

def mobilenet_v3_small_p3d(**kwargs):
    """Create P3D version of MobileNetV3_Small for video processing"""
    model = MobileNetV3_Small_P3D(**kwargs)
    return model

if __name__ == '__main__':
    # Test the P3D MobileNetV3_Small model
    print("Testing MobileNetV3_Small_P3D...")

    # Create model
    model = mobilenet_v3_small_p3d(num_classes=256, modality='RGB', depth_3d=8)

    # Create dummy input (B, C, T, H, W) = (2, 3, 16, 112, 112)
    dummy_input = torch.randn(2, 3, 16, 112, 112)

    print(f"Input shape: {dummy_input.shape}")
    print(f"Model input size: {model.input_size}")

    # Forward pass
    with torch.no_grad():
        output = model(dummy_input)

    print(f"Output shape: {output.shape}")
    print("Model test completed successfully!")

    # Compare with original 2D model
    print("\nTesting original MobileNetV3_Small...")
    model_2d = mobilenet_v3_small(num_classes=256)
    dummy_input_2d = torch.randn(2, 8, 112, 112)  # 2D input

    with torch.no_grad():
        output_2d = model_2d(dummy_input_2d)

    print(f"2D Input shape: {dummy_input_2d.shape}")
    print(f"2D Output shape: {output_2d.shape}")

    # Print model comparison
    def count_parameters(model):
        return sum(p.numel() for p in model.parameters() if p.requires_grad)

    print(f"\nParameter comparison:")
    print(f"P3D MobileNetV3_Small: {count_parameters(model):,} parameters")
    print(f"Original MobileNetV3_Small: {count_parameters(model_2d):,} parameters")

