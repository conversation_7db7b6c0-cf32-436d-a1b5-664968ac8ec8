import torch
import torch.nn as nn
import torch.nn.functional as F

class ConvBNReLU(nn.Sequential):
    def __init__(self, in_planes, out_planes, kernel_size=3, stride=1, groups=1):
        padding = (kernel_size - 1) // 2
        super(ConvBNReLU, self).__init__(
            nn.Conv2d(in_planes, out_planes, kernel_size, stride, padding, groups=groups, bias=False),
            nn.BatchNorm2d(out_planes),
            nn.ReLU6(inplace=True)
        )

class SqueezeExcitation(nn.Module):
    def __init__(self, in_channels, reduced_dim):
        super(SqueezeExcitation, self).__init__()
        self.se = nn.Sequential(
            nn.AdaptiveAvgPool2d(1),
            nn.Conv2d(in_channels, reduced_dim, 1),
            nn.ReLU6(inplace=True),
            nn.Conv2d(reduced_dim, in_channels, 1),
            nn.Sigmoid()
        )

    def forward(self, x):
        return x * self.se(x)

class InvertedResidual(nn.Module):
    def __init__(self, inp, oup, stride, expand_ratio):
        super(InvertedResidual, self).__init__()
        self.stride = stride
        assert stride in [1, 2]

        hidden_dim = int(round(inp * expand_ratio))
        self.use_res_connect = self.stride == 1 and inp == oup

        layers = []
        if expand_ratio != 1:
            layers.append(ConvBNReLU(inp, hidden_dim, kernel_size=1))
        
        layers.extend([
            ConvBNReLU(hidden_dim, hidden_dim, stride=stride, groups=hidden_dim),
            SqueezeExcitation(hidden_dim, max(1, hidden_dim // 4)),
            nn.Conv2d(hidden_dim, oup, 1, 1, 0, bias=False),
            nn.BatchNorm2d(oup),
        ])
        self.conv = nn.Sequential(*layers)

    def forward(self, x):
        if self.use_res_connect:
            return x + self.conv(x)
        else:
            return self.conv(x)

class MobileNetV3_Large(nn.Module):
    def __init__(self, num_classes=256):
        super(MobileNetV3_Large, self).__init__()
        
        # Modified first layer with 8 input channels
        self.features = nn.Sequential(
            ConvBNReLU(8, 16, 3, stride=2),
            InvertedResidual(16, 16, 1, expand_ratio=1),
            InvertedResidual(16, 24, 2, expand_ratio=4),
            InvertedResidual(24, 24, 1, expand_ratio=3),
            InvertedResidual(24, 40, 2, expand_ratio=3),
            InvertedResidual(40, 40, 1, expand_ratio=3),
            InvertedResidual(40, 40, 1, expand_ratio=3),
            InvertedResidual(40, 80, 2, expand_ratio=6),
            InvertedResidual(80, 80, 1, expand_ratio=2.5),
            InvertedResidual(80, 80, 1, expand_ratio=2.3),
            InvertedResidual(80, 80, 1, expand_ratio=2.3),
            InvertedResidual(80, 112, 1, expand_ratio=6),
            InvertedResidual(112, 112, 1, expand_ratio=6),
            InvertedResidual(112, 160, 2, expand_ratio=6),
            InvertedResidual(160, 160, 1, expand_ratio=6),
            InvertedResidual(160, 160, 1, expand_ratio=6),
            ConvBNReLU(160, 960, kernel_size=1),
            nn.AdaptiveAvgPool2d(1)
        )
        
        self.classifier = nn.Sequential(
            nn.Linear(960, 1280),
            nn.Hardswish(inplace=True),
            nn.Dropout(p=0.2, inplace=True),
            nn.Linear(1280, num_classes),
        )

        self.__init_weight()

    def forward(self, x):
        x = (x / 255 - 0.4161) / 0.1688
        
        x = self.features(x)
        x = x.flatten(1)
        x = self.classifier(x)
        return x
    
    def __init_weight(self):
        for m in self.modules():
            if isinstance(m, nn.Conv3d) or isinstance(m, nn.Conv2d) or isinstance(m, nn.Linear):
                torch.nn.init.kaiming_normal_(m.weight)              
            elif isinstance(m, nn.BatchNorm3d) or isinstance(m, nn.LayerNorm):
                m.weight.data.fill_(1)
                m.bias.data.zero_()

def mobilenet_v3_large(**kwargs):
    model = MobileNetV3_Large(**kwargs)
    return model

def export_to_onnx(model, output_path="mobilenetv3_large.onnx"):
    """Export MobileNetV3_Large model to ONNX format"""
    # Create dummy input with shape (1,8,112,112)
    dummy_input = torch.randn(1, 8, 112, 112)
    
    # Export the model
    torch.onnx.export(
        model,
        dummy_input,
        output_path,
        input_names=["input"],
        output_names=["output"],
        # dynamic_axes={
        #     "input": {0: "batch_size"},
        #     "output": {0: "batch_size"}
        # }
    )
    print(f"Model exported to {output_path}")

class MobileNetV3_Small(nn.Module):
    def __init__(self, num_classes=256):
        super(MobileNetV3_Small, self).__init__()
        # 参考官方结构，输入通道为8
        self.features = nn.Sequential(
            ConvBNReLU(8, 16, 3, stride=2),
            InvertedResidual(16, 16, 2, expand_ratio=1),
            InvertedResidual(16, 24, 2, expand_ratio=4.5),
            InvertedResidual(24, 24, 1, expand_ratio=3.67),
            InvertedResidual(24, 40, 2, expand_ratio=4),
            InvertedResidual(40, 40, 1, expand_ratio=6),
            InvertedResidual(40, 40, 1, expand_ratio=6),
            InvertedResidual(40, 48, 1, expand_ratio=3),
            InvertedResidual(48, 48, 1, expand_ratio=3),
            InvertedResidual(48, 96, 2, expand_ratio=6),
            InvertedResidual(96, 96, 1, expand_ratio=6),
            InvertedResidual(96, 96, 1, expand_ratio=6),
            ConvBNReLU(96, 576, kernel_size=1),
            nn.AdaptiveAvgPool2d(1)
        )
        self.classifier = nn.Sequential(
            nn.Linear(576, 1024),
            nn.Hardswish(inplace=True),
            nn.Dropout(p=0.2, inplace=True),
            nn.Linear(1024, num_classes),
        )
        self.__init_weight()

    def forward(self, x):
        x = (x / 255 - 0.4161) / 0.1688
        x = self.features(x)
        x = x.flatten(1)
        x = self.classifier(x)
        return x

    def __init_weight(self):
        for m in self.modules():
            if isinstance(m, nn.Conv3d) or isinstance(m, nn.Conv2d) or isinstance(m, nn.Linear):
                torch.nn.init.kaiming_normal_(m.weight)
            elif isinstance(m, nn.BatchNorm3d) or isinstance(m, nn.LayerNorm):
                m.weight.data.fill_(1)
                m.bias.data.zero_()

def mobilenet_v3_small(**kwargs):
    model = MobileNetV3_Small(**kwargs)
    return model

# ==================== 3D Version based on P3D63 ====================

class Conv3dBNReLU_P3D(nn.Module):
    """P3D-style 3D convolution: split into spatial (1x3x3) and temporal (3x1x1) convolutions"""
    def __init__(self, in_planes, out_planes, kernel_size=3, stride=1, groups=1):
        super(Conv3dBNReLU_P3D, self).__init__()
        padding = (kernel_size - 1) // 2
        
        # Spatial convolution: 1x3x3
        self.conv_s = nn.Conv3d(in_planes, out_planes, kernel_size=(1, kernel_size, kernel_size), 
                               stride=(1, stride, stride), 
                               padding=(0, padding, padding), 
                               groups=groups, bias=False)
        self.bn_s = nn.BatchNorm3d(out_planes)
        
        # Temporal convolution: 3x1x1
        self.conv_t = nn.Conv3d(out_planes, out_planes, kernel_size=(kernel_size, 1, 1), 
                               stride=(stride, 1, 1), 
                               padding=(padding, 0, 0), 
                               groups=groups, bias=False)
        self.bn_t = nn.BatchNorm3d(out_planes)
        
        self.relu = nn.ReLU6(inplace=True)

    def forward(self, x):
        # Spatial convolution first
        x = self.conv_s(x)
        x = self.bn_s(x)
        x = self.relu(x)
        
        # Then temporal convolution
        x = self.conv_t(x)
        x = self.bn_t(x)
        x = self.relu(x)
        
        return x

class SqueezeExcitation3D_P3D(nn.Module):
    """3D version of SqueezeExcitation with P3D-style convolutions"""
    def __init__(self, in_channels, reduced_dim):
        super(SqueezeExcitation3D_P3D, self).__init__()
        self.se = nn.Sequential(
            nn.AdaptiveAvgPool3d(1),
            nn.Conv3d(in_channels, reduced_dim, 1),
            nn.ReLU6(inplace=True),
            nn.Conv3d(reduced_dim, in_channels, 1),
            nn.Sigmoid()
        )

    def forward(self, x):
        return x * self.se(x)

class InvertedResidual3D_P3D(nn.Module):
    """3D version of InvertedResidual with P3D-style convolutions"""
    def __init__(self, inp, oup, stride, expand_ratio):
        super(InvertedResidual3D_P3D, self).__init__()
        self.stride = stride
        assert stride in [1, 2]

        hidden_dim = int(round(inp * expand_ratio))
        self.use_res_connect = self.stride == 1 and inp == oup

        layers = []
        if expand_ratio != 1:
            # Pointwise expansion: 1x1x1 convolution
            layers.append(nn.Sequential(
                nn.Conv3d(inp, hidden_dim, 1, 1, 0, bias=False),
                nn.BatchNorm3d(hidden_dim),
                nn.ReLU6(inplace=True)
            ))
        
        # Depthwise separable convolution with P3D style
        layers.extend([
            # Spatial depthwise: 1x3x3
            nn.Conv3d(hidden_dim, hidden_dim, kernel_size=(1, 3, 3), 
                     stride=(1, stride, stride), padding=(0, 1, 1), 
                     groups=hidden_dim, bias=False),
            nn.BatchNorm3d(hidden_dim),
            nn.ReLU6(inplace=True),
            
            # Temporal depthwise: 3x1x1
            nn.Conv3d(hidden_dim, hidden_dim, kernel_size=(3, 1, 1), 
                     stride=(stride, 1, 1), padding=(1, 0, 0), 
                     groups=hidden_dim, bias=False),
            nn.BatchNorm3d(hidden_dim),
            nn.ReLU6(inplace=True),
            
            # SqueezeExcitation
            SqueezeExcitation3D_P3D(hidden_dim, max(1, hidden_dim // 4)),
            
            # Pointwise linear: 1x1x1
            nn.Conv3d(hidden_dim, oup, 1, 1, 0, bias=False),
            nn.BatchNorm3d(oup),
        ])
        self.conv = nn.Sequential(*layers)

    def forward(self, x):
        if self.use_res_connect:
            return x + self.conv(x)
        else:
            return self.conv(x)

class MobileNetV3_Small_3D(nn.Module):
    """3D version of MobileNetV3_Small based on P3D63 architecture"""
    def __init__(self, num_classes=256):
        super(MobileNetV3_Small_3D, self).__init__()
        
        # Input: (B, 1, T=8, H=112, W=112)
        self.features = nn.Sequential(
            # First layer: 1 -> 16, spatial stride 2
            Conv3dBNReLU_P3D(1, 16, 3, stride=2),
            
            # InvertedResidual blocks
            InvertedResidual3D_P3D(16, 16, 2, expand_ratio=1),
            InvertedResidual3D_P3D(16, 24, 2, expand_ratio=4.5),
            InvertedResidual3D_P3D(24, 24, 1, expand_ratio=3.67),
            InvertedResidual3D_P3D(24, 40, 2, expand_ratio=4),
            InvertedResidual3D_P3D(40, 40, 1, expand_ratio=6),
            InvertedResidual3D_P3D(40, 40, 1, expand_ratio=6),
            InvertedResidual3D_P3D(40, 48, 1, expand_ratio=3),
            InvertedResidual3D_P3D(48, 48, 1, expand_ratio=3),
            InvertedResidual3D_P3D(48, 96, 2, expand_ratio=6),
            InvertedResidual3D_P3D(96, 96, 1, expand_ratio=6),
            InvertedResidual3D_P3D(96, 96, 1, expand_ratio=6),
            
            # Final conv layer: 96 -> 576
            nn.Sequential(
                nn.Conv3d(96, 576, 1, 1, 0, bias=False),
                nn.BatchNorm3d(576),
                nn.ReLU6(inplace=True)
            ),
            
            # Global average pooling across spatial and temporal dimensions
            nn.AdaptiveAvgPool3d(1)
        )
        
        self.classifier = nn.Sequential(
            nn.Linear(576, 1024),
            nn.Hardswish(inplace=True),
            nn.Dropout(p=0.2, inplace=True),
            nn.Linear(1024, num_classes),
        )
        
        self.__init_weight()

    def forward(self, x):
        # Input normalization: (B, 8, T=8, H=112, W=112)
        x = (x / 255 - 0.4161) / 0.1688
        
        x = self.features(x)
        # x shape after features: (B, 576, 1, 1, 1)
        x = x.flatten(1)  # (B, 576)
        x = self.classifier(x)
        return x

    def __init_weight(self):
        for m in self.modules():
            if isinstance(m, nn.Conv3d) or isinstance(m, nn.Conv2d) or isinstance(m, nn.Linear):
                torch.nn.init.kaiming_normal_(m.weight)
            elif isinstance(m, nn.BatchNorm3d) or isinstance(m, nn.BatchNorm2d):
                m.weight.data.fill_(1)
                m.bias.data.zero_()

def mobilenet_v3_small_3d(**kwargs):
    """Construct a MobileNetV3_Small_3D model based on P3D63 architecture"""
    model = MobileNetV3_Small_3D(**kwargs)
    return model

def export_to_onnx_3d(model, output_path="mobilenetv3_small_3d.onnx"):
    """Export MobileNetV3_Small_3D model to ONNX format"""
    # Create dummy input with shape (1, 1, 8, 112, 112)
    dummy_input = torch.randn(1, 1, 8, 112, 112)
    
    # Export the model
    torch.onnx.export(
        model,
        dummy_input,
        output_path,
        input_names=["input"],
        output_names=["output"],
    )
    print(f"3D Model exported to {output_path}")

# Test the 3D model
if __name__ == "__main__":
    # Test 3D version
    print("Testing MobileNetV3_Small_3D...")
    model_3d = mobilenet_v3_small_3d()
    model_3d.eval()
    
    # Test input: (B, 1, T=8, H=112, W=112)
    input_3d = torch.ones(1, 1, 8, 112, 112) * 255
    output_3d = model_3d(input_3d)
    print(f"3D Model output shape: {output_3d.shape}")
    print(f"3D Model output: {output_3d}")
    
    # Export 3D model
    export_to_onnx_3d(model_3d)
    print("3D Model ONNX export completed. Check mobilenetv3_small_3d.onnx")
    
    # Original 2D test
    print("\nTesting original MobileNetV3_Small...")
    model = mobilenet_v3_small()
    model.eval()
    input_2d = torch.ones(1, 8, 112, 112) * 255
    output_2d = model(input_2d)
    print(f"2D Model output shape: {output_2d.shape}")
    print(f"2D Model output: {output_2d}")


