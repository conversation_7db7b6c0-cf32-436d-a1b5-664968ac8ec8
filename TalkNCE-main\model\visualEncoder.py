##
# ResNet18 Pretrained network to extract lip embedding
# This code is modified based on https://github.com/lordmartian/deep_avsr
##

import torch
import torch.nn as nn
import torch.nn.functional as F
from model.attentionLayer import attentionLayer


class hswish(nn.Module):
    def forward(self, x):
        out = x * F.relu6(x + 3, inplace=True) / 6
        return out

class MobileNetV3Block(nn.Module):
    def __init__(self, in_channels, out_channels, kernel_size, stride, 
                 expand_ratio, activation='relu', se_ratio=0.25):
        super(MobileNetV3Block, self).__init__()
        self.stride = stride
        hidden_dim = round(in_channels * expand_ratio)
        self.use_res_connect = self.stride == 1 and in_channels == out_channels
        layers = []
        if expand_ratio != 1:
            layers.append(nn.Conv2d(in_channels, hidden_dim, 1, 1, 0, bias=False))
            layers.append(nn.BatchNorm2d(hidden_dim))
            layers.append(nn.ReLU6(inplace=True) if activation == 'relu' else hswish())
        layers.append(nn.Conv2d(hidden_dim, hidden_dim, kernel_size, stride, 
                               kernel_size//2, groups=hidden_dim, bias=False))
        layers.append(nn.BatchNorm2d(hidden_dim))
        layers.append(nn.ReLU6(inplace=True) if activation == 'relu' else hswish())

        if se_ratio is not None:
            squeeze_channels = max(1, int(in_channels * se_ratio))
            layers.append(SELayer(hidden_dim, squeeze_channels))
        layers.append(nn.Conv2d(hidden_dim, out_channels, 1, 1, 0, bias=False))
        layers.append(nn.BatchNorm2d(out_channels))
        self.block = nn.Sequential(*layers)
        

    def forward(self, x):
        if self.use_res_connect:
            return x + self.block(x)
        else:
            return self.block(x)

class SELayer(nn.Module):
    def __init__(self, channel, reduction=4):
        super(SELayer, self).__init__()
        self.avg_pool = nn.AdaptiveAvgPool2d(1)
        self.fc = nn.Sequential(
            nn.Linear(channel, channel // reduction, bias=False),
            nn.ReLU(inplace=True),
            nn.Linear(channel // reduction, channel, bias=False),
            nn.Sigmoid()
        )
    def forward(self, x):
        b, c, _, _ = x.size()
        y = self.avg_pool(x).view(b, c)
        y = self.fc(y).view(b, c, 1, 1)
        return x * y.expand_as(x)


class MobileNetV3(nn.Module):

    def __init__(self):
        super(MobileNetV3, self).__init__()
        self.features = nn.Sequential(
            # First conv layer
            nn.Conv2d(64, 16, 3, 2, 1, bias=False),
            nn.BatchNorm2d(16),
            hswish(),
            MobileNetV3Block(16, 16, 3, 1, 1, 'relu', 0.25),
            MobileNetV3Block(16, 24, 3, 2, 4, 'relu', 0.25),
            MobileNetV3Block(24, 24, 3, 1, 3, 'relu', 0.25),
            MobileNetV3Block(24, 40, 5, 2, 3, 'relu', 0.25),
            MobileNetV3Block(40, 40, 5, 1, 3, 'relu', 0.25),
            MobileNetV3Block(40, 40, 5, 1, 3, 'relu', 0.25),
            MobileNetV3Block(40, 80, 3, 2, 6, 'hswish'),
            MobileNetV3Block(80, 80, 3, 1, 2.5, 'hswish'),
            MobileNetV3Block(80, 80, 3, 1, 2.3, 'hswish'),
            MobileNetV3Block(80, 80, 3, 1, 2.3, 'hswish'),
            MobileNetV3Block(80, 112, 3, 1, 6, 'hswish'),
            MobileNetV3Block(112, 112, 3, 1, 6, 'hswish'),
            MobileNetV3Block(112, 160, 5, 2, 6, 'hswish'),
            MobileNetV3Block(160, 160, 5, 1, 6, 'hswish'),
            MobileNetV3Block(160, 160, 5, 1, 6, 'hswish'),
            nn.Conv2d(160, 960, 1, 1, 0, bias=False),
            nn.BatchNorm2d(960),
            hswish(),
            nn.AdaptiveAvgPool2d(1),
            nn.Conv2d(960, 512, 1, 1, 0, bias=False),
            hswish()
        )

    def forward(self, x):
        x = self.features(x)
        return x.view(x.size(0), -1)


class GlobalLayerNorm(nn.Module):

    def __init__(self, channel_size):
        super(GlobalLayerNorm, self).__init__()
        self.gamma = nn.Parameter(torch.Tensor(1, channel_size, 1))    # [1, N, 1]
        self.beta = nn.Parameter(torch.Tensor(1, channel_size, 1))    # [1, N, 1]
        self.reset_parameters()

    def reset_parameters(self):
        self.gamma.data.fill_(1)
        self.beta.data.zero_()

    def forward(self, y):
        mean = y.mean(dim=1, keepdim=True).mean(dim=2, keepdim=True)    #[M, 1, 1]
        var = (torch.pow(y - mean, 2)).mean(dim=1, keepdim=True).mean(dim=2, keepdim=True)
        gLN_y = self.gamma * (y - mean) / torch.pow(var + 1e-8, 0.5) + self.beta
        return gLN_y


class visualFrontend(nn.Module):
    """
    A visual feature extraction module. Generates a 512-dim feature vector per video frame.
    Architecture: A 3D convolution block followed by MobileNetV3.
    """

    def __init__(self, cfg):
        self.cfg = cfg
        super(visualFrontend, self).__init__()
        self.frontend3D = nn.Sequential(
            nn.Conv3d(1, 64, kernel_size=(5, 7, 7), stride=(1, 2, 2), padding=(2, 3, 3),
                      bias=False), nn.BatchNorm3d(64, momentum=0.01, eps=0.001), nn.ReLU(),
            nn.MaxPool3d(kernel_size=(1, 3, 3), stride=(1, 2, 2), padding=(0, 1, 1)))
        self.mobilenet = MobileNetV3()
        return

    def forward(self, inputBatch):
        inputBatch = inputBatch.transpose(0, 1).transpose(1, 2)
        batchsize = inputBatch.shape[0]
        batch = self.frontend3D(inputBatch)

        batch = batch.transpose(1, 2)
        batch = batch.reshape(batch.shape[0] * batch.shape[1], batch.shape[2], batch.shape[3],
                              batch.shape[4])
        outputBatch = self.mobilenet(batch)
        outputBatch = outputBatch.reshape(batchsize, -1, 512)
        outputBatch = outputBatch.transpose(1, 2)
        outputBatch = outputBatch.transpose(1, 2).transpose(0, 1)
        return outputBatch

class DSConv1d(nn.Module):

    def __init__(self):
        super(DSConv1d, self).__init__()
        self.net = nn.Sequential(
            nn.ReLU(),
            nn.BatchNorm1d(512),
            nn.Conv1d(512, 512, 3, stride=1, padding=1, dilation=1, groups=512, bias=False),
            nn.PReLU(),
            GlobalLayerNorm(512),
            nn.Conv1d(512, 512, 1, bias=False),
        )

    def forward(self, x):
        out = self.net(x)
        return out + x


class visualTCN(nn.Module):

    def __init__(self):
        super(visualTCN, self).__init__()
        stacks = []
        for x in range(5):
            stacks += [DSConv1d()]
        self.net = nn.Sequential(*stacks)    # Visual Temporal Network V-TCN

    def forward(self, x):
        out = self.net(x)
        return out


class visualConv1D(nn.Module):

    def __init__(self):
        super(visualConv1D, self).__init__()
        self.net = nn.Sequential(
            nn.Conv1d(512, 256, 5, stride=1, padding=2),
            nn.BatchNorm1d(256),
            nn.ReLU(),
            nn.Conv1d(256, 128, 1),
        )

    def forward(self, x):
        out = self.net(x)
        return out
