import torch
import torch.nn as nn
import torch.nn.functional as F

class hswish(nn.Module):
    """h-swish activation function"""
    def forward(self, x):
        return x * F.relu6(x + 3, inplace=True) / 6

class hsigmoid(nn.Module):
    """h-sigmoid activation function"""
    def forward(self, x):
        return F.relu6(x + 3, inplace=True) / 6

class SEBlock(nn.Module):
    """Squeeze-and-Excitation block"""
    def __init__(self, channel, reduction=4):
        super(SEBlock, self).__init__()
        self.avg_pool = nn.AdaptiveAvgPool2d(1)
        self.fc = nn.Sequential(
            nn.Linear(channel, channel // reduction),
            nn.ReLU(inplace=True),
            nn.Linear(channel // reduction, channel),
            hsigmoid()
        )
    
    def forward(self, x):
        b, c, _, _ = x.size()
        y = self.avg_pool(x).view(b, c)
        y = self.fc(y).view(b, c, 1, 1)
        return x * y

class Bottleneck(nn.Module):
    """MobileNetV3 bottleneck block"""
    def __init__(self, in_channels, out_channels, kernel_size, 
                 stride, expansion, se=False, nl='RE'):
        super(Bottleneck, self).__init__()
        self.se = se
        padding = (kernel_size - 1) // 2
        
        # Expansion phase
        self.conv1 = nn.Conv2d(in_channels, expansion, 1, bias=False)
        self.bn1 = nn.BatchNorm2d(expansion)
        
        # Depthwise convolution
        self.conv2 = nn.Conv2d(expansion, expansion, kernel_size, 
                              stride, padding, groups=expansion, bias=False)
        self.bn2 = nn.BatchNorm2d(expansion)
        
        # Squeeze-and-excitation
        if se:
            self.se_block = SEBlock(expansion)
            
        # Projection phase
        self.conv3 = nn.Conv2d(expansion, out_channels, 1, bias=False)
        self.bn3 = nn.BatchNorm2d(out_channels)
        
        # Activation functions
        if nl == 'RE':
            self.act1 = nn.ReLU(inplace=True)
            self.act2 = nn.ReLU(inplace=True)
        elif nl == 'HS':
            self.act1 = hswish()
            self.act2 = hswish()
            
        self.stride = stride
        self.in_channels = in_channels
        self.out_channels = out_channels
        
    def forward(self, x):
        residual = x
        
        out = self.act1(self.bn1(self.conv1(x)))
        out = self.act2(self.bn2(self.conv2(out)))
        
        if self.se:
            out = self.se_block(out)
            
        out = self.bn3(self.conv3(out))
        
        if self.stride == 1 and self.in_channels == self.out_channels:
            out += residual
            
        return out

class MobileNetV3_Small(nn.Module):
    """MobileNetV3 Small architecture"""
    def __init__(self, num_classes=1000):
        super(MobileNetV3_Small, self).__init__()
        self.conv1 = nn.Conv2d(3, 16, 3, stride=2, padding=1, bias=False)
        self.bn1 = nn.BatchNorm2d(16)
        self.hs1 = hswish()
        
        # Bottleneck layers configuration
        self.bneck = nn.Sequential(
            # in, out, kernel, stride, expansion, se, nl
            Bottleneck(16, 16, 3, 2, 16, True, 'RE'),
            Bottleneck(16, 24, 3, 2, 72, False, 'RE'),
            Bottleneck(24, 24, 3, 1, 88, False, 'RE'),
            Bottleneck(24, 40, 5, 2, 96, True, 'HS'),
            Bottleneck(40, 40, 5, 1, 240, True, 'HS'),
            Bottleneck(40, 40, 5, 1, 240, True, 'HS'),
            Bottleneck(40, 48, 5, 1, 120, True, 'HS'),
            Bottleneck(48, 48, 5, 1, 144, True, 'HS'),
            Bottleneck(48, 96, 5, 2, 288, True, 'HS'),
            Bottleneck(96, 96, 5, 1, 576, True, 'HS'),
            Bottleneck(96, 96, 5, 1, 576, True, 'HS'),
        )
        
        self.conv2 = nn.Conv2d(96, 576, 1, stride=1, padding=0, bias=False)
        self.bn2 = nn.BatchNorm2d(576)
        self.hs2 = hswish()
        
        self.avgpool = nn.AdaptiveAvgPool2d(1)
        self.conv3 = nn.Conv2d(576, 1024, 1, stride=1, padding=0, bias=True)
        self.hs3 = hswish()
        self.conv4 = nn.Conv2d(1024, num_classes, 1, stride=1, padding=0, bias=True)
        
    def forward(self, x):
        x = self.hs1(self.bn1(self.conv1(x)))
        x = self.bneck(x)
        x = self.hs2(self.bn2(self.conv2(x)))
        x = self.avgpool(x)
        x = self.hs3(self.conv3(x))
        x = self.conv4(x)
        x = x.view(x.size(0), -1)
        return x

class MobileNetV3_Small_05(nn.Module):
    """MobileNetV3 Small 0.5 version (half width)"""
    def __init__(self, num_classes=1000):
        super(MobileNetV3_Small_05, self).__init__()
        self.conv1 = nn.Conv2d(3, 8, 3, stride=2, padding=1, bias=False)  # 16 -> 8
        self.bn1 = nn.BatchNorm2d(8)
        self.hs1 = hswish()
        
        # Bottleneck layers configuration (all channels halved)
        self.bneck = nn.Sequential(
            # in, out, kernel, stride, expansion, se, nl
            Bottleneck(8, 8, 3, 2, 8, True, 'RE'),  # 16,16,16 -> 8,8,8
            Bottleneck(8, 12, 3, 2, 36, False, 'RE'),  # 16,24,72 -> 8,12,36
            Bottleneck(12, 12, 3, 1, 44, False, 'RE'),  # 24,24,88 -> 12,12,44
            Bottleneck(12, 20, 5, 2, 48, True, 'HS'),  # 24,40,96 -> 12,20,48
            Bottleneck(20, 20, 5, 1, 120, True, 'HS'),  # 40,40,240 -> 20,20,120
            Bottleneck(20, 20, 5, 1, 120, True, 'HS'),  # 40,40,240 -> 20,20,120
            Bottleneck(20, 24, 5, 1, 60, True, 'HS'),  # 40,48,120 -> 20,24,60
            Bottleneck(24, 24, 5, 1, 72, True, 'HS'),  # 48,48,144 -> 24,24,72
            Bottleneck(24, 48, 5, 2, 144, True, 'HS'),  # 48,96,288 -> 24,48,144
            Bottleneck(48, 48, 5, 1, 288, True, 'HS'),  # 96,96,576 -> 48,48,288
            Bottleneck(48, 48, 5, 1, 288, True, 'HS'),  # 96,96,576 -> 48,48,288
        )
        
        self.conv2 = nn.Conv2d(48, 288, 1, stride=1, padding=0, bias=False)  # 96,576 -> 48,288
        self.bn2 = nn.BatchNorm2d(288)
        self.hs2 = hswish()
        
        self.avgpool = nn.AdaptiveAvgPool2d(1)
        self.conv3 = nn.Conv2d(288, 512, 1, stride=1, padding=0, bias=True)  # 576,1024 -> 288,512
        self.hs3 = hswish()
        self.conv4 = nn.Conv2d(512, num_classes, 1, stride=1, padding=0, bias=True)
        
    def forward(self, x):
        x = self.hs1(self.bn1(self.conv1(x)))
        x = self.bneck(x)
        x = self.hs2(self.bn2(self.conv2(x)))
        x = self.avgpool(x)
        x = self.hs3(self.conv3(x))
        x = self.conv4(x)
        x = x.view(x.size(0), -1)
        return x
