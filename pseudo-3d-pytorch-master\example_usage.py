#!/usr/bin/env python3
"""
P3D MobileNetV3_Small 使用示例
演示如何使用改进后的MobileNetV3_Small进行视频处理
"""

import torch
import torch.nn as nn
from mobilenet import mobilenet_v3_small_p3d, mobilenet_v3_small

def compare_models():
    """比较原始和P3D版本的MobileNetV3_Small"""
    print("=" * 60)
    print("MobileNetV3_Small 模型对比")
    print("=" * 60)
    
    # 创建模型
    model_2d = mobilenet_v3_small(num_classes=256)
    model_p3d = mobilenet_v3_small_p3d(
        num_classes=256, 
        modality='RGB', 
        depth_3d=8,
        ST_struc=('A', 'B', 'C')
    )
    
    # 计算参数量
    def count_parameters(model):
        return sum(p.numel() for p in model.parameters() if p.requires_grad)
    
    params_2d = count_parameters(model_2d)
    params_p3d = count_parameters(model_p3d)
    
    print(f"原始 MobileNetV3_Small:     {params_2d:,} 参数")
    print(f"P3D MobileNetV3_Small:      {params_p3d:,} 参数")
    print(f"参数增长比例:               {params_p3d/params_2d:.2f}x")
    print()
    
    return model_2d, model_p3d

def test_inference():
    """测试推理性能"""
    print("=" * 60)
    print("推理测试")
    print("=" * 60)
    
    # 创建P3D模型
    model = mobilenet_v3_small_p3d(num_classes=256, modality='RGB', depth_3d=8)
    model.eval()
    
    # 创建测试数据
    batch_size = 2
    channels = 3  # RGB
    frames = 16
    height, width = 112, 112
    
    input_tensor = torch.randn(batch_size, channels, frames, height, width)
    
    print(f"输入形状: {input_tensor.shape}")
    print(f"输入大小: {input_tensor.numel():,} 元素")
    
    # 推理
    with torch.no_grad():
        output = model(input_tensor)
    
    print(f"输出形状: {output.shape}")
    print(f"预期输出: (batch_size * frames, num_classes)")
    print()
    
    return output

def test_different_configurations():
    """测试不同配置的P3D模型"""
    print("=" * 60)
    print("不同配置测试")
    print("=" * 60)
    
    configs = [
        {'depth_3d': 4, 'ST_struc': ('A',)},
        {'depth_3d': 6, 'ST_struc': ('A', 'B')},
        {'depth_3d': 8, 'ST_struc': ('A', 'B', 'C')},
        {'depth_3d': 10, 'ST_struc': ('A', 'B', 'C')},
    ]
    
    input_tensor = torch.randn(1, 3, 16, 112, 112)
    
    for i, config in enumerate(configs):
        print(f"配置 {i+1}: depth_3d={config['depth_3d']}, ST_struc={config['ST_struc']}")
        
        model = mobilenet_v3_small_p3d(
            num_classes=256, 
            modality='RGB',
            **config
        )
        
        # 计算参数量
        params = sum(p.numel() for p in model.parameters() if p.requires_grad)
        print(f"  参数量: {params:,}")
        
        # 测试推理
        model.eval()
        with torch.no_grad():
            output = model(input_tensor)
        print(f"  输出形状: {output.shape}")
        print()

def test_optical_flow():
    """测试光流模态"""
    print("=" * 60)
    print("光流模态测试")
    print("=" * 60)
    
    # 创建光流模型 (2通道输入)
    model = mobilenet_v3_small_p3d(
        num_classes=256, 
        modality='Flow',  # 光流模态
        depth_3d=8
    )
    
    # 光流输入 (2通道: x和y方向的光流)
    input_flow = torch.randn(2, 2, 16, 112, 112)
    
    print(f"光流输入形状: {input_flow.shape}")
    print(f"模型输入配置: {model.input_size}")
    print(f"输入均值: {model.input_mean}")
    print(f"输入标准差: {model.input_std}")
    
    model.eval()
    with torch.no_grad():
        output = model(input_flow)
    
    print(f"输出形状: {output.shape}")
    print()

def analyze_model_structure():
    """分析模型结构"""
    print("=" * 60)
    print("模型结构分析")
    print("=" * 60)
    
    model = mobilenet_v3_small_p3d(num_classes=256, depth_3d=8)
    
    print("主要组件:")
    print(f"  - 初始3D卷积: {model.conv1_custom}")
    print(f"  - 3D批归一化: {model.bn1}")
    print(f"  - 3D时间池化: {model.maxpool_3d}")
    print(f"  - 特征提取层数: {len(model.features)}")
    print(f"  - 3D处理深度: {model.depth_3d}")
    print(f"  - ST结构模式: {model.ST_struc}")
    print()
    
    # 分析每一层
    print("倒残差块配置:")
    configs = [
        (16, 16, 2, 1), (16, 24, 2, 4.5), (24, 24, 1, 3.67), (24, 40, 2, 4),
        (40, 40, 1, 6), (40, 40, 1, 6), (40, 48, 1, 3), (48, 48, 1, 3),
        (48, 96, 2, 6), (96, 96, 1, 6), (96, 96, 1, 6)
    ]
    
    for i, (inp, oup, stride, expand_ratio) in enumerate(configs):
        is_3d = i < model.depth_3d
        mode = "3D" if is_3d else "2D"
        st_mode = list(model.ST_struc)[i % len(model.ST_struc)] if is_3d else "N/A"
        print(f"  层 {i:2d}: {inp:2d}→{oup:2d}, stride={stride}, expand={expand_ratio:.1f}, "
              f"模式={mode}, ST={st_mode}")

def main():
    """主函数"""
    print("P3D MobileNetV3_Small 使用示例")
    print("基于P3D63思路改进的轻量级视频理解模型")
    print()
    
    # 模型对比
    compare_models()
    
    # 推理测试
    test_inference()
    
    # 不同配置测试
    test_different_configurations()
    
    # 光流模态测试
    test_optical_flow()
    
    # 模型结构分析
    analyze_model_structure()
    
    print("=" * 60)
    print("测试完成！")
    print("=" * 60)

if __name__ == '__main__':
    main()
