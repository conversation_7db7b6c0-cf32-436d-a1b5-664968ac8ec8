def compare_files(file1_path, file2_path, threshold=0.01):
    """
    Compare two text files line by line.
    Return line numbers where the absolute difference between numbers is greater than threshold.
    """
    differing_lines = []
    
    try:
        with open(file1_path, 'r') as f1, open(file2_path, 'r') as f2:
            for line_num, (line1, line2) in enumerate(zip(f1, f2), start=1):
                try:
                    num1 = float(line1.strip())
                    num2 = float(line2.strip())
                    if abs(num1 - num2) > threshold:
                        differing_lines.append((line_num, num1, num2))
                except ValueError:
                    # Skip non-numeric lines
                    continue
                    
    except FileNotFoundError as e:
        print(f"Error: {e}")
        return []
    
    return differing_lines

if __name__ == "__main__":
    import sys
    # if len(sys.argv) != 3:
    #     print("Usage: python compare.py file1.txt file2.txt")
    #     sys.exit(1)
        
    file1 = r"D:\out.txt"
    file2 = r"D:\output.txt"
    
    result = compare_files(file1, file2)
    if result:
        print("Lines with difference > 0.01:", result)
    else:
        print("All lines have difference <= 0.01")

