{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import math\n", "import numbers\n", "import torch\n", "from torch import nn\n", "from torch.nn import functional as F\n", "\n", "class GaussianSmoothing(nn.Module):\n", "    \"\"\"\n", "    Apply gaussian smoothing on a\n", "    1d, 2d or 3d tensor. Filtering is performed seperately for each channel\n", "    in the input using a depthwise convolution.\n", "    Arguments:\n", "        channels (int, sequence): Number of channels of the input tensors. Output will\n", "            have this number of channels as well.\n", "        kernel_size (int, sequence): Size of the gaussian kernel.\n", "        sigma (float, sequence): Standard deviation of the gaussian kernel.\n", "        dim (int, optional): The number of dimensions of the data.\n", "            Default value is 2 (spatial).\n", "    \"\"\"\n", "    def __init__(self, channels, kernel_size, sigma, dim=2):\n", "        super(<PERSON><PERSON><PERSON><PERSON><PERSON>othing, self).__init__()\n", "        if isinstance(kernel_size, numbers.Number):\n", "            kernel_size = [kernel_size] * dim\n", "        if isinstance(sigma, numbers.Number):\n", "            sigma = [sigma] * dim\n", "\n", "        # The gaussian kernel is the product of the\n", "        # gaussian function of each dimension.\n", "        kernel = 1\n", "        meshgrids = torch.meshgrid(\n", "            [\n", "                torch.arange(size, dtype=torch.float32)\n", "                for size in kernel_size\n", "            ]\n", "        )\n", "        for size, std, mgrid in zip(kernel_size, sigma, meshgrids):\n", "            mean = (size - 1) / 2\n", "            kernel *= 1 / (std * math.sqrt(2 * math.pi)) * \\\n", "                      torch.exp(-((mgrid - mean) / std) ** 2 / 2)\n", "\n", "        # Make sure sum of values in gaussian kernel equals 1.\n", "        kernel = kernel / torch.sum(kernel)\n", "\n", "        # Reshape to depthwise convolutional weight\n", "        kernel = kernel.view(1, 1, *kernel.size())\n", "        kernel = kernel.repeat(channels, *[1] * (kernel.dim() - 1))\n", "\n", "        self.register_buffer('weight', kernel)\n", "        self.groups = channels\n", "\n", "        if dim == 1:\n", "            self.conv = F.conv1d\n", "        elif dim == 2:\n", "            self.conv = F.conv2d\n", "        elif dim == 3:\n", "            self.conv = F.conv3d\n", "        else:\n", "            raise RuntimeError(\n", "                'Only 1, 2 and 3 dimensions are supported. Received {}.'.format(dim)\n", "            )\n", "\n", "    def forward(self, input):\n", "        \"\"\"\n", "        Apply gaussian filter to input.\n", "        Arguments:\n", "            input (torch.Tensor): Input to apply gaussian filter on.\n", "        Returns:\n", "            filtered (torch.Tensor): Filtered output.\n", "        \"\"\"\n", "        return self.conv(input, weight=self.weight, groups=self.groups)"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [], "source": ["smoothing = GaussianSmoothing(1024, 5, 1, dim=1)\n", "input = torch.rand(4, 16, 100, 64)\n", "b = input.shape[0]\n", "numhead = input.shape[1]\n", "t = input.shape[2]\n", "c = input.shape[3]\n", "input = input.permute(0,1,3,2)\n", "input = input.reshape(b, numhead*c, t)\n", "input = F.pad(input, (2, 2), mode='reflect')\n", "output = smoothing(input)\n", "output = output.reshape(b, numhead, c, t)\n", "output = output.permute(0,1,3,2)"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [{"data": {"text/plain": ["torch.<PERSON><PERSON>([4, 16, 100, 64])"]}, "execution_count": 19, "metadata": {}, "output_type": "execute_result"}], "source": ["input\n", "output.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["input = torch.rand(4, 16, 100, 64)\n", "attention = torch.normal(0,1 size = input.shape)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.3"}}, "nbformat": 4, "nbformat_minor": 4}