# P3D MobileNetV3_Small: 基于P3D63思路的轻量级视频理解模型

## 概述

本项目基于P3D63的设计思路，成功将MobileNetV3_Small扩展为支持视频处理的3D网络。通过借鉴P3D的伪3D卷积分解技术，在保持MobileNetV3轻量化特性的同时，实现了高效的时空特征提取。

## 主要特性

- ✅ **P3D分解**: 将3D卷积分解为空间卷积(1×3×3)和时间卷积(3×1×1)
- ✅ **ST结构**: 支持三种时空连接模式 (A: 串行, B: 并行, C: 残差)
- ✅ **混合架构**: 前N层使用3D卷积，后续层使用2D卷积
- ✅ **多模态**: 支持RGB和光流两种输入模态
- ✅ **轻量化**: 相比直接3D化，参数增长控制在合理范围内
- ✅ **灵活配置**: 可调节3D层数和ST结构组合

## 核心改进

### 1. P3D卷积分解
```python
# 空间卷积: 处理空间信息
conv_S: 1×3×3 卷积

# 时间卷积: 处理时间信息  
conv_T: 3×1×1 卷积
```

### 2. ST结构模式
- **ST-A**: input → conv_S → conv_T → output
- **ST-B**: input → (conv_S + conv_T) → output  
- **ST-C**: input → conv_S → (conv_T + conv_S) → output

### 3. 智能3D/2D转换
- 前`depth_3d`层使用3D处理
- 自动在边界处转换张量维度
- 后续层使用高效的2D处理

## 性能对比

| 模型 | 参数量 | 输入格式 | 输出格式 |
|------|--------|----------|----------|
| 原始 MobileNetV3_Small | 1,745,542 | (B, 8, H, W) | (B, 256) |
| P3D MobileNetV3_Small | 3,810,542 | (B, 3, T, H, W) | (B×T, 256) |
| **参数增长** | **2.18x** | **支持视频** | **时序输出** |

## 使用方法

### 基本使用
```python
from mobilenet import mobilenet_v3_small_p3d

# 创建模型
model = mobilenet_v3_small_p3d(
    num_classes=256,
    modality='RGB',           # 或 'Flow'
    depth_3d=8,              # 前8层使用3D卷积
    ST_struc=('A','B','C')   # ST结构循环模式
)

# 输入视频数据 (batch, channels, frames, height, width)
video_input = torch.randn(2, 3, 16, 112, 112)
output = model(video_input)  # (4, 256)
```

### 不同配置示例
```python
# 轻量配置 (更少3D层)
model_light = mobilenet_v3_small_p3d(depth_3d=4, ST_struc=('A',))

# 标准配置
model_standard = mobilenet_v3_small_p3d(depth_3d=8, ST_struc=('A','B','C'))

# 光流模态
model_flow = mobilenet_v3_small_p3d(modality='Flow', depth_3d=6)
```

## 文件结构

```
pseudo-3d-pytorch-master/
├── mobilenet.py                    # 主要实现文件
├── p3d_model.py                   # P3D63参考实现
├── P3D_MobileNetV3_Analysis.md    # 详细技术分析
├── example_usage.py               # 使用示例
└── README_P3D_MobileNetV3.md      # 本文档
```

## 技术细节

### 网络架构
1. **输入层**: 3D卷积 (1×3×3) + 批归一化 + ReLU6
2. **特征提取**: 11个P3D倒残差块
   - 前8层: 3D处理 + P3D分解 + ST结构
   - 后3层: 2D处理 (高效)
3. **输出层**: 全局平均池化 + 分类器

### 关键创新点
- **InvertedResidualP3D**: P3D版本的倒残差块
- **自动维度转换**: 智能处理3D→2D转换
- **时间池化**: 在适当位置降低时间维度
- **SE注意力**: 3D版本的Squeeze-and-Excitation

## 实验结果

运行 `python example_usage.py` 查看完整测试结果：

```bash
# 基本测试
python mobilenet.py

# 详细示例
python example_usage.py
```

### 典型输出
- **输入**: (2, 3, 16, 112, 112) - 2个视频，3通道，16帧，112×112分辨率
- **输出**: (4, 256) - 4个时间步的特征向量，每个256维
- **参数**: 3,810,542 (相比原版增长2.18倍)

## 应用场景

- 🎬 **视频分类**: 动作识别、场景理解
- 👥 **人体分析**: 姿态估计、行为分析  
- 🗣️ **语音视觉**: 唇语识别、说话人检测
- 📱 **移动端**: 资源受限环境的视频理解
- 🔄 **光流处理**: 运动信息提取

## 优势总结

1. **效率**: P3D分解显著降低计算复杂度
2. **精度**: 专门的时空处理提升特征质量
3. **灵活**: 可配置的3D/2D混合架构
4. **兼容**: 保持MobileNetV3的轻量化优势
5. **实用**: 支持多种视频理解任务

## 引用

如果使用本实现，请引用相关论文：

```bibtex
@inproceedings{qiu2017learning,
  title={Learning spatio-temporal representation with pseudo-3d residual networks},
  author={Qiu, Zhaofan and Yao, Ting and Mei, Tao},
  booktitle={proceedings of the IEEE International Conference on Computer Vision},
  pages={5533--5541},
  year={2017}
}
```

---

**作者**: 基于P3D63思路的MobileNetV3_Small改进实现  
**日期**: 2024年  
**许可**: 遵循原项目许可协议
